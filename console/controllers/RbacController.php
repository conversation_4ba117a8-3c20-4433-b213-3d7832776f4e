<?php

namespace console\controllers;

use common\rbac\SubjectLeaderRule;
use yii\console\Controller;
use yii\rbac\DbManager;
use yii\rbac\Permission;
use yii\rbac\Role;
use Yii;

/**
 * RBAC权限管理控制器
 */
class RbacController extends Controller
{
    /**
     * 初始化权限系统
     */
    public function actionInit()
    {
        $auth = Yii::$app->authManager;
        
        // 清除现有权限
        $auth->removeAll();
        
        // 创建学科组长权限验证规则
        $subjectLeaderRule = new SubjectLeaderRule();
        $auth->add($subjectLeaderRule);
        
        // 创建基础权限
        $permissions = $this->createPermissions($auth);
        
        // 创建角色
        $roles = $this->createRoles($auth);
        
        // 分配权限给角色
        $this->assignPermissionsToRoles($auth, $permissions, $roles);
        
        echo "权限系统初始化完成！\n";
    }

    /**
     * 创建权限
     */
    private function createPermissions(DbManager $auth): array
    {
        $permissions = [];

        // 校本题库相关权限（只保留需要特殊权限控制的3个权限）
        $schoolPaperPermissions = [
            'school-paper-manage' => '校本题库管理',
            'school-paper-audit' => '校本题库审核',
            'paper-tag-manage' => '标签管理',
        ];

        foreach ($schoolPaperPermissions as $name => $description) {
            $permission = $auth->createPermission($name);
            $permission->description = $description;

            // 所有学科组长相关权限都添加规则，绑定学科和年级
            $permission->ruleName = 'subjectLeaderRule';

            $auth->add($permission);
            $permissions[$name] = $permission;
        }

        // 注意：后台管理系统使用独立的权限系统，这里只管理前台学校业务权限

        return $permissions;
    }

    /**
     * 创建角色
     */
    private function createRoles(DbManager $auth): array
    {
        $roles = [];

        // 创建教师角色（默认角色，所有学校用户都有）
        $teacher = $auth->createRole('teacher');
        $teacher->description = '教师';
        $auth->add($teacher);
        $roles['teacher'] = $teacher;

        // 创建学科组长角色
        $subjectLeader = $auth->createRole('subject-leader');
        $subjectLeader->description = '学科组长';
        $auth->add($subjectLeader);
        $roles['subject-leader'] = $subjectLeader;

        // 创建学校管理员角色
        $schoolAdmin = $auth->createRole('school-admin');
        $schoolAdmin->description = '学校管理员';
        $auth->add($schoolAdmin);
        $roles['school-admin'] = $schoolAdmin;

        return $roles;
    }

    /**
     * 分配权限给角色
     */
    private function assignPermissionsToRoles(DbManager $auth, array $permissions, array $roles): void
    {
        // 教师角色：默认角色，无特殊权限
        // 校本题库查看和提交对所有学校用户开放，无需权限验证

        // 学科组长权限：拥有3个需要学科年级绑定的权限
        $auth->addChild($roles['subject-leader'], $permissions['school-paper-manage']);
        $auth->addChild($roles['subject-leader'], $permissions['school-paper-audit']);
        $auth->addChild($roles['subject-leader'], $permissions['paper-tag-manage']);

        // 学校管理员权限（继承学科组长权限，但不受学科年级限制）
        $auth->addChild($roles['school-admin'], $roles['subject-leader']);
    }

    /**
     * 为用户分配学科组长权限范围
     */
    public function actionAssignSubjectLeader($userId, $schoolId, $courseCode, $grade = null)
    {
        $auth = Yii::$app->authManager;
        
        // 分配学科组长角色
        $subjectLeaderRole = $auth->getRole('subject-leader');
        if (!$auth->getAssignment('subject-leader', $userId)) {
            $auth->assign($subjectLeaderRole, $userId);
        }

        // 设置权限范围
        $scopes = [
            [
                'course_code' => $courseCode,
                'grade' => $grade,
            ]
        ];

        $success = \common\models\rbac\SubjectLeaderScope::setUserPermissionScope($userId, $schoolId, $scopes);
        
        if ($success) {
            echo "成功为用户 {$userId} 分配学科组长权限：科目 {$courseCode}";
            if ($grade) {
                echo "，年级 {$grade}";
            } else {
                echo "，所有年级";
            }
            echo "\n";
        } else {
            echo "分配权限失败！\n";
        }
    }

    /**
     * 移除用户的学科组长权限
     */
    public function actionRevokeSubjectLeader($userId, $schoolId)
    {
        $auth = Yii::$app->authManager;
        
        // 移除学科组长角色
        $auth->revoke($auth->getRole('subject-leader'), $userId);
        
        // 清除权限范围
        \common\models\rbac\SubjectLeaderScope::deleteAll(['user_id' => $userId, 'school_id' => $schoolId]);
        
        echo "成功移除用户 {$userId} 的学科组长权限\n";
    }

    /**
     * 为所有学校用户分配教师角色
     */
    public function actionAssignTeacherRoles($schoolId = null)
    {
        $result = \common\helpers\TeacherRoleHelper::batchAssignTeacherRole($schoolId);

        echo "教师角色分配完成：\n";
        echo "总用户数：{$result['total']}\n";
        echo "成功分配：{$result['success']}\n";
        echo "分配失败：{$result['failed']}\n";
    }

    /**
     * 查看用户权限
     */
    public function actionViewUserPermissions($userId)
    {
        $auth = Yii::$app->authManager;

        echo "用户 {$userId} 的权限信息：\n";
        echo "角色：\n";

        $roles = $auth->getRolesByUser($userId);
        foreach ($roles as $role) {
            echo "  - {$role->name}: {$role->description}\n";
        }

        echo "权限：\n";
        $permissions = $auth->getPermissionsByUser($userId);
        foreach ($permissions as $permission) {
            echo "  - {$permission->name}: {$permission->description}\n";
        }

        // 如果是学科组长，显示权限范围
        if (isset($roles['subject-leader'])) {
            $user = \common\models\base\User::findOne($userId);
            if ($user && $user->school) {
                $scopes = \common\models\rbac\SubjectLeaderScope::getUserPermissionScope($userId, $user->school->id);
                if (!empty($scopes)) {
                    echo "学科组长权限范围：\n";
                    foreach ($scopes as $courseCode => $grades) {
                        echo "  - 科目 {$courseCode}：";
                        if (in_array('*', $grades)) {
                            echo "所有年级\n";
                        } else {
                            echo "年级 " . implode(', ', $grades) . "\n";
                        }
                    }
                }
            }
        }
    }
}
