<?php

namespace backend\controllers;

use common\enums\Grade;
use common\models\base\Course;
use common\models\base\User;
use common\models\rbac\SubjectLeaderScope;
use common\models\school\School;
use common\models\school\SchoolUser;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use Yii;

/**
 * 学科组长权限管理控制器
 */
class SubjectLeaderController extends BaseController
{
    public function behaviors(): array
    {
        $behaviors = parent::behaviors();
        $behaviors['access']['rules'][] = [
            'allow' => true,
            'roles' => ['administrator'],
            'permissions' => ['user-manage'],
        ];

        return $behaviors;
    }

    public function menuKeyMap(): array
    {
        return [
            'index' => 'user-manage',
            'assign' => 'user-manage',
            'revoke' => 'user-manage',
        ];
    }

    /**
     * 学科组长权限管理首页
     */
    public function actionIndex()
    {
        $searchModel = new \yii\base\DynamicModel(['school_id', 'course_code', 'grade']);
        $searchModel->addRule(['school_id', 'course_code', 'grade'], 'safe');

        $query = SubjectLeaderScope::find()
            ->joinWith(['user', 'school', 'course'])
            ->orderBy(['school_id' => SORT_ASC, 'course_code' => SORT_ASC, 'grade' => SORT_ASC]);

        $params = Yii::$app->request->queryParams;
        $searchModel->load($params);

        if ($searchModel->school_id) {
            $query->andWhere(['subject_leader_scope.school_id' => $searchModel->school_id]);
        }
        if ($searchModel->course_code) {
            $query->andWhere(['subject_leader_scope.course_code' => $searchModel->course_code]);
        }
        if ($searchModel->grade) {
            $query->andWhere(['subject_leader_scope.grade' => $searchModel->grade]);
        }

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => 20,
            ],
        ]);

        $schools = School::find()->all();
        $courses = Course::find()->all();
        $grades = Grade::cases();

        return $this->render('index', [
            'dataProvider' => $dataProvider,
            'searchModel' => $searchModel,
            'schools' => $schools,
            'courses' => $courses,
            'grades' => $grades,
        ]);
    }

    /**
     * 分配学科组长权限页面
     */
    public function actionAssign($userId = null)
    {
        if ($userId) {
            $user = $this->findUser($userId);
            $school = $user->school;
            
            if (!$school) {
                throw new NotFoundHttpException('用户未关联学校');
            }

            // 获取当前权限范围
            $currentScopes = SubjectLeaderScope::find()
                ->where(['user_id' => $userId, 'school_id' => $school->id])
                ->all();

            if (Yii::$app->request->isPost) {
                return $this->handleAssignPost($user, $school);
            }

            $courses = Course::find()->all();
            $grades = Grade::cases();

            return $this->render('assign', [
                'user' => $user,
                'school' => $school,
                'currentScopes' => $currentScopes,
                'courses' => $courses,
                'grades' => $grades,
            ]);
        }

        // 显示用户选择页面
        $schoolUsers = SchoolUser::find()
            ->joinWith('user')
            ->joinWith('school')
            ->all();

        return $this->render('select-user', [
            'schoolUsers' => $schoolUsers,
        ]);
    }

    /**
     * 处理分配权限的POST请求
     */
    private function handleAssignPost(User $user, School $school): Response
    {
        $post = Yii::$app->request->post();
        $scopes = $post['scopes'] ?? [];

        $auth = Yii::$app->authManager;
        
        // 分配学科组长角色
        $subjectLeaderRole = $auth->getRole('subject-leader');
        if (!$auth->getAssignment('subject-leader', $user->id)) {
            $auth->assign($subjectLeaderRole, $user->id);
        }

        // 处理权限范围数据
        $scopeData = [];
        foreach ($scopes as $scope) {
            if (!empty($scope['course_code'])) {
                $scopeData[] = [
                    'course_code' => $scope['course_code'],
                    'grade' => !empty($scope['grade']) ? $scope['grade'] : null,
                ];
            }
        }

        $success = SubjectLeaderScope::setUserPermissionScope($user->id, $school->id, $scopeData);

        if ($success) {
            Yii::$app->session->setFlash('success', '学科组长权限分配成功');
        } else {
            Yii::$app->session->setFlash('error', '学科组长权限分配失败');
        }

        return $this->redirect(['index']);
    }

    /**
     * 移除学科组长权限
     */
    public function actionRevoke($userId)
    {
        $user = $this->findUser($userId);
        $school = $user->school;
        
        if (!$school) {
            throw new NotFoundHttpException('用户未关联学校');
        }

        $auth = Yii::$app->authManager;
        
        // 移除学科组长角色
        $subjectLeaderRole = $auth->getRole('subject-leader');
        $auth->revoke($subjectLeaderRole, $userId);
        
        // 清除权限范围
        SubjectLeaderScope::deleteAll(['user_id' => $userId, 'school_id' => $school->id]);
        
        Yii::$app->session->setFlash('success', '学科组长权限移除成功');
        
        return $this->redirect(['index']);
    }

    /**
     * 获取用户的权限范围（AJAX接口）
     */
    public function actionGetUserScopes($userId)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $user = $this->findUser($userId);
        $school = $user->school;
        
        if (!$school) {
            return ['success' => false, 'message' => '用户未关联学校'];
        }

        $scopes = SubjectLeaderScope::find()
            ->where(['user_id' => $userId, 'school_id' => $school->id])
            ->all();

        $result = [];
        foreach ($scopes as $scope) {
            $result[] = [
                'course_code' => $scope->course_code,
                'course_name' => $scope->course->name ?? '',
                'grade' => $scope->grade,
                'grade_name' => $scope->grade ? $scope->grade->getLabel() : '所有年级',
            ];
        }

        return ['success' => true, 'data' => $result];
    }

    /**
     * 批量操作（AJAX接口）
     */
    public function actionBatchOperation()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $post = Yii::$app->request->post();
        $operation = $post['operation'] ?? '';
        $ids = $post['ids'] ?? [];

        if (empty($ids) || !is_array($ids)) {
            return ['success' => false, 'message' => '请选择要操作的项目'];
        }

        switch ($operation) {
            case 'delete':
                $count = SubjectLeaderScope::deleteAll(['id' => $ids]);
                return ['success' => true, 'message' => "成功删除 {$count} 条记录"];
                
            default:
                return ['success' => false, 'message' => '不支持的操作'];
        }
    }

    /**
     * 查找用户
     */
    private function findUser($userId): User
    {
        $user = User::findOne($userId);
        if (!$user) {
            throw new NotFoundHttpException('用户不存在');
        }
        return $user;
    }
}
