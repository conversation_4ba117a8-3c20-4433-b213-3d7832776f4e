<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $dataProvider yii\data\ActiveDataProvider */
/* @var $searchModel yii\base\DynamicModel */
/* @var $schools common\models\school\School[] */
/* @var $courses common\models\base\Course[] */
/* @var $grades common\enums\Grade[] */

$this->title = '学科组长权限管理';
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="subject-leader-index">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title"><?= Html::encode($this->title) ?></h3>
            <div class="card-tools">
                <?= Html::a('分配权限', ['assign'], ['class' => 'btn btn-primary btn-sm']) ?>
            </div>
        </div>

        <div class="card-body">
            <!-- 搜索表单 -->
            <div class="row mb-3">
                <?= Html::beginForm(['index'], 'get', ['class' => 'form-inline']) ?>
                
                <div class="col-md-3">
                    <?= Html::dropDownList('school_id', $searchModel->school_id, 
                        \yii\helpers\ArrayHelper::map($schools, 'id', 'name'), 
                        ['prompt' => '选择学校', 'class' => 'form-control']) ?>
                </div>
                
                <div class="col-md-3">
                    <?= Html::dropDownList('course_code', $searchModel->course_code, 
                        \yii\helpers\ArrayHelper::map($courses, 'code', 'name'), 
                        ['prompt' => '选择科目', 'class' => 'form-control']) ?>
                </div>
                
                <div class="col-md-3">
                    <?= Html::dropDownList('grade', $searchModel->grade, 
                        \yii\helpers\ArrayHelper::map($grades, 'value', 'label'), 
                        ['prompt' => '选择年级', 'class' => 'form-control']) ?>
                </div>
                
                <div class="col-md-3">
                    <?= Html::submitButton('搜索', ['class' => 'btn btn-outline-secondary']) ?>
                    <?= Html::a('重置', ['index'], ['class' => 'btn btn-outline-secondary']) ?>
                </div>
                
                <?= Html::endForm() ?>
            </div>

            <!-- 数据表格 -->
            <?= GridView::widget([
                'dataProvider' => $dataProvider,
                'tableOptions' => ['class' => 'table table-striped table-bordered'],
                'columns' => [
                    ['class' => 'yii\grid\SerialColumn'],
                    
                    [
                        'attribute' => 'user.real_name',
                        'label' => '用户姓名',
                        'value' => function ($model) {
                            return $model->user->real_name ?? '';
                        }
                    ],
                    
                    [
                        'attribute' => 'school.name',
                        'label' => '学校',
                        'value' => function ($model) {
                            return $model->school->name ?? '';
                        }
                    ],
                    
                    [
                        'attribute' => 'course.name',
                        'label' => '科目',
                        'value' => function ($model) {
                            return $model->course->name ?? '';
                        }
                    ],
                    
                    [
                        'attribute' => 'grade',
                        'label' => '年级',
                        'value' => function ($model) {
                            return $model->grade ? $model->grade->getLabel() : '所有年级';
                        }
                    ],
                    
                    [
                        'attribute' => 'created_at',
                        'label' => '分配时间',
                        'format' => 'datetime',
                    ],

                    [
                        'class' => 'yii\grid\ActionColumn',
                        'template' => '{assign} {revoke}',
                        'buttons' => [
                            'assign' => function ($url, $model, $key) {
                                return Html::a('编辑', ['assign', 'userId' => $model->user_id], [
                                    'class' => 'btn btn-sm btn-outline-primary',
                                    'title' => '编辑权限'
                                ]);
                            },
                            'revoke' => function ($url, $model, $key) {
                                return Html::a('移除', ['revoke', 'userId' => $model->user_id], [
                                    'class' => 'btn btn-sm btn-outline-danger',
                                    'title' => '移除权限',
                                    'data' => [
                                        'confirm' => '确定要移除该用户的学科组长权限吗？',
                                        'method' => 'post',
                                    ],
                                ]);
                            },
                        ],
                    ],
                ],
            ]); ?>
        </div>
    </div>
</div>

<style>
.form-inline .form-control {
    margin-right: 10px;
    margin-bottom: 10px;
}
</style>
