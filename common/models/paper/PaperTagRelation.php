<?php

namespace common\models\paper;

use common\models\base\Paper;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * 试卷标签关联模型
 *
 * @property int $id
 * @property int $paper_id 试卷ID
 * @property int $tag_id 标签ID
 * @property int $is_main 是否主标签：0-否，1-是
 * @property int $created_at 创建时间
 *
 * @property Paper $paper 试卷
 * @property PaperTag $tag 标签
 */
class PaperTagRelation extends ActiveRecord
{
    const IS_MAIN_NO = 0;  // 非主标签
    const IS_MAIN_YES = 1; // 主标签

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%paper_tag_relation}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors(): array
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'updatedAtAttribute' => false, // 不需要 updated_at 字段
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['paper_id', 'tag_id'], 'required'],
            [['paper_id', 'tag_id', 'is_main', 'created_at'], 'integer'],
            [['is_main'], 'in', 'range' => [self::IS_MAIN_NO, self::IS_MAIN_YES]],
            [['is_main'], 'default', 'value' => self::IS_MAIN_NO],
            
            // 唯一性验证
            [['paper_id'], 'unique', 'targetAttribute' => ['paper_id', 'tag_id']],
            
            // 验证试卷和标签存在
            [['paper_id'], 'exist', 'targetClass' => Paper::class, 'targetAttribute' => 'id'],
            [['tag_id'], 'exist', 'targetClass' => PaperTag::class, 'targetAttribute' => 'id'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'paper_id' => '试卷ID',
            'tag_id' => '标签ID',
            'is_main' => '是否主标签',
            'created_at' => '创建时间',
        ];
    }

    /**
     * 获取试卷关联
     */
    public function getPaper()
    {
        return $this->hasOne(Paper::class, ['id' => 'paper_id']);
    }

    /**
     * 获取标签关联
     */
    public function getTag()
    {
        return $this->hasOne(PaperTag::class, ['id' => 'tag_id']);
    }

    /**
     * 判断是否为主标签
     */
    public function isMainTag(): bool
    {
        return $this->is_main == self::IS_MAIN_YES;
    }

    /**
     * 为试卷批量设置标签
     *
     * @param int $paperId 试卷ID
     * @param array $tagIds 标签ID数组
     * @param int|null $mainTagId 主标签ID
     * @return bool
     */
    public static function setTagsForPaper(int $paperId, array $tagIds, ?int $mainTagId = null): bool
    {
        $transaction = static::getDb()->beginTransaction();
        
        try {
            // 删除现有关联
            static::deleteAll(['paper_id' => $paperId]);
            
            // 添加新关联
            foreach ($tagIds as $tagId) {
                $relation = new static();
                $relation->paper_id = $paperId;
                $relation->tag_id = $tagId;
                $relation->is_main = ($tagId == $mainTagId) ? self::IS_MAIN_YES : self::IS_MAIN_NO;
                
                if (!$relation->save()) {
                    throw new \Exception('保存标签关联失败');
                }
            }
            
            $transaction->commit();
            return true;
        } catch (\Exception $e) {
            $transaction->rollBack();
            return false;
        }
    }

    /**
     * 获取试卷的所有标签
     *
     * @param int $paperId 试卷ID
     * @return PaperTag[]
     */
    public static function getTagsForPaper(int $paperId): array
    {
        return PaperTag::find()
            ->joinWith('tagRelations')
            ->where(['paper_tag_relation.paper_id' => $paperId])
            ->orderBy('paper_tag_relation.is_main DESC, paper_tag.sort_order ASC')
            ->all();
    }

    /**
     * 获取试卷的主标签
     *
     * @param int $paperId 试卷ID
     * @return PaperTag|null
     */
    public static function getMainTagForPaper(int $paperId): ?PaperTag
    {
        return PaperTag::find()
            ->joinWith('tagRelations')
            ->where([
                'paper_tag_relation.paper_id' => $paperId,
                'paper_tag_relation.is_main' => self::IS_MAIN_YES
            ])
            ->one();
    }

    /**
     * 获取标签下的试卷数量
     *
     * @param int $tagId 标签ID
     * @return int
     */
    public static function getPaperCountForTag(int $tagId): int
    {
        return static::find()->where(['tag_id' => $tagId])->count();
    }

    /**
     * 获取多个标签下的试卷ID
     *
     * @param array $tagIds 标签ID数组
     * @param bool $matchAll 是否需要匹配所有标签（true）还是匹配任一标签（false）
     * @return array 试卷ID数组
     */
    public static function getPaperIdsByTags(array $tagIds, bool $matchAll = false): array
    {
        if (empty($tagIds)) {
            return [];
        }

        $query = static::find()
            ->select('paper_id')
            ->where(['tag_id' => $tagIds]);

        if ($matchAll) {
            // 需要匹配所有标签
            $query->groupBy('paper_id')
                ->having('COUNT(DISTINCT tag_id) = :tag_count', [':tag_count' => count($tagIds)]);
        }

        return $query->column();
    }
}
