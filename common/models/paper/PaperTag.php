<?php

namespace common\models\paper;

use common\models\base\User;
use common\models\school\School;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * 校本试卷标签模型
 *
 * @property int $id
 * @property string $name 标签名称
 * @property int|null $school_id 学校ID，NULL表示系统级标签
 * @property string $course_code 课程代码
 * @property int $type 标签类型：0-系统，1-自定义
 * @property int|null $parent_id 父级标签ID
 * @property int $sort_order 排序
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int|null $created_by 创建人ID
 * @property int|null $updated_by 更新人ID
 *
 * @property School $school 学校
 * @property User $creator 创建人
 * @property User $updater 更新人
 * @property PaperTag $parent 父级标签
 * @property PaperTag[] $children 子级标签
 * @property PaperTagRelation[] $tagRelations 标签关联
 */
class PaperTag extends ActiveRecord
{
    const TYPE_SYSTEM = 0; // 系统级标签
    const TYPE_CUSTOM = 1; // 自定义标签

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%school_paper_tag}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors(): array
    {
        return [
            TimestampBehavior::class,
            BlameableBehavior::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['name', 'course_code'], 'required'],
            [['school_id', 'type', 'parent_id', 'sort_order', 'created_at', 'updated_at', 'created_by', 'updated_by'], 'integer'],
            [['name'], 'string', 'max' => 50],
            [['course_code'], 'string', 'max' => 30],
            [['type'], 'in', 'range' => [self::TYPE_SYSTEM, self::TYPE_CUSTOM]],
            [['sort_order'], 'default', 'value' => 0],
            
            // 验证父级标签不能是自己
            [['parent_id'], 'validateParentId'],
            
            // 验证标签名称在同一学校、同一课程下唯一
            [['name'], 'unique', 'targetAttribute' => ['name', 'school_id', 'course_code', 'parent_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'name' => '标签名称',
            'school_id' => '学校ID',
            'course_code' => '课程代码',
            'type' => '标签类型',
            'parent_id' => '父级标签ID',
            'sort_order' => '排序',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
            'created_by' => '创建人',
            'updated_by' => '更新人',
        ];
    }

    /**
     * 验证父级标签ID
     */
    public function validateParentId($attribute, $params)
    {
        if ($this->parent_id == $this->id) {
            $this->addError($attribute, '父级标签不能是自己');
        }
    }

    /**
     * 获取学校关联
     */
    public function getSchool()
    {
        return $this->hasOne(School::class, ['id' => 'school_id']);
    }

    /**
     * 获取创建人关联
     */
    public function getCreator()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * 获取更新人关联
     */
    public function getUpdater()
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }

    /**
     * 获取父级标签关联
     */
    public function getParent()
    {
        return $this->hasOne(static::class, ['id' => 'parent_id']);
    }

    /**
     * 获取子级标签关联
     */
    public function getChildren()
    {
        return $this->hasMany(static::class, ['parent_id' => 'id'])->orderBy('sort_order ASC, id ASC');
    }

    /**
     * 获取标签关联关系
     */
    public function getTagRelations()
    {
        return $this->hasMany(PaperTagRelation::class, ['tag_id' => 'id']);
    }

    /**
     * 获取标签类型文本
     */
    public function getTypeText(): string
    {
        return $this->type == self::TYPE_SYSTEM ? '系统' : '自定义';
    }

    /**
     * 判断是否为系统标签
     */
    public function isSystemTag(): bool
    {
        return $this->type == self::TYPE_SYSTEM;
    }

    /**
     * 判断是否为自定义标签
     */
    public function isCustomTag(): bool
    {
        return $this->type == self::TYPE_CUSTOM;
    }

    /**
     * 判断是否为根级标签
     */
    public function isRootTag(): bool
    {
        return $this->parent_id === null;
    }

    /**
     * 获取标签的完整路径
     */
    public function getFullPath(): string
    {
        $path = [$this->name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * 获取学校的标签树
     *
     * @param int|null $schoolId 学校ID，null表示系统标签
     * @param string $courseCode 课程代码
     * @return array
     */
    public static function getTagTree(?int $schoolId, string $courseCode): array
    {
        $tags = static::find()
            ->where(['school_id' => $schoolId, 'course_code' => $courseCode])
            ->orderBy('sort_order ASC, id ASC')
            ->all();

        return static::buildTree($tags);
    }

    /**
     * 构建标签树
     *
     * @param PaperTag[] $tags
     * @param int|null $parentId
     * @return array
     */
    private static function buildTree(array $tags, ?int $parentId = null): array
    {
        $tree = [];
        
        foreach ($tags as $tag) {
            if ($tag->parent_id == $parentId) {
                $node = $tag->toArray();
                $node['children'] = static::buildTree($tags, $tag->id);
                $tree[] = $node;
            }
        }
        
        return $tree;
    }

    /**
     * 获取所有子标签ID（包括自己）
     */
    public function getAllChildrenIds(): array
    {
        $ids = [$this->id];
        
        foreach ($this->children as $child) {
            $ids = array_merge($ids, $child->getAllChildrenIds());
        }
        
        return $ids;
    }

    /**
     * 删除前检查是否有关联数据
     */
    public function beforeDelete(): bool
    {
        if (!parent::beforeDelete()) {
            return false;
        }

        // 检查是否有子标签
        if ($this->getChildren()->exists()) {
            $this->addError('id', '存在子标签，无法删除');
            return false;
        }

        // 检查是否有关联的试卷
        if ($this->getTagRelations()->exists()) {
            $this->addError('id', '存在关联的试卷，无法删除');
            return false;
        }

        return true;
    }
}
