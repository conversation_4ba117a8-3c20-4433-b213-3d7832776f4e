<?php

namespace common\models\paper;

use common\models\base\User;
use common\models\school\School;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * 校本试卷审核日志模型
 *
 * @property int $id
 * @property int $paper_id 试卷ID
 * @property int $school_id 学校ID
 * @property int $status 审核状态：0-提交审核，1-审核通过，2-审核退回
 * @property string|null $comment 审核意见
 * @property int $created_by 操作人ID
 * @property int $created_at 创建时间
 *
 * @property School $school 学校
 * @property User $creator 操作人
 */
class SchoolPaperAuditLog extends ActiveRecord
{
    const STATUS_SUBMITTED = 0; // 提交审核
    const STATUS_APPROVED = 1;  // 审核通过
    const STATUS_REJECTED = 2;  // 审核退回

    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%school_paper_audit_log}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors(): array
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'updatedAtAttribute' => false, // 不需要 updated_at 字段
            ],
            [
                'class' => BlameableBehavior::class,
                'updatedByAttribute' => false, // 不需要 updated_by 字段
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['paper_id', 'school_id', 'status'], 'required'],
            [['paper_id', 'school_id', 'status', 'created_by', 'created_at'], 'integer'],
            [['comment'], 'string'],
            [['status'], 'in', 'range' => [self::STATUS_SUBMITTED, self::STATUS_APPROVED, self::STATUS_REJECTED]],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'paper_id' => '试卷ID',
            'school_id' => '学校ID',
            'status' => '审核状态',
            'comment' => '审核意见',
            'created_by' => '操作人',
            'created_at' => '创建时间',
        ];
    }

    /**
     * 获取学校关联
     */
    public function getSchool()
    {
        return $this->hasOne(School::class, ['id' => 'school_id']);
    }

    /**
     * 获取操作人关联
     */
    public function getCreator()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * 获取状态文本
     */
    public function getStatusText(): string
    {
        $statusMap = [
            self::STATUS_SUBMITTED => '提交审核',
            self::STATUS_APPROVED => '审核通过',
            self::STATUS_REJECTED => '审核退回',
        ];

        return $statusMap[$this->status] ?? '未知状态';
    }

    /**
     * 判断是否为提交审核状态
     */
    public function isSubmitted(): bool
    {
        return $this->status == self::STATUS_SUBMITTED;
    }

    /**
     * 判断是否为审核通过状态
     */
    public function isApproved(): bool
    {
        return $this->status == self::STATUS_APPROVED;
    }

    /**
     * 判断是否为审核退回状态
     */
    public function isRejected(): bool
    {
        return $this->status == self::STATUS_REJECTED;
    }

    /**
     * 记录审核日志
     *
     * @param int $paperId 试卷ID
     * @param int $schoolId 学校ID
     * @param int $status 审核状态
     * @param string|null $comment 审核意见
     * @param int|null $createdBy 操作人ID
     * @return bool
     */
    public static function log(int $paperId, int $schoolId, int $status, ?string $comment = null, ?int $createdBy = null): bool
    {
        $log = new static();
        $log->paper_id = $paperId;
        $log->school_id = $schoolId;
        $log->status = $status;
        $log->comment = $comment;
        
        if ($createdBy !== null) {
            $log->created_by = $createdBy;
        }

        return $log->save();
    }

    /**
     * 获取试卷的审核历史
     *
     * @param int $paperId 试卷ID
     * @param int $schoolId 学校ID
     * @return static[]
     */
    public static function getAuditHistory(int $paperId, int $schoolId): array
    {
        return static::find()
            ->where(['paper_id' => $paperId, 'school_id' => $schoolId])
            ->orderBy('created_at DESC')
            ->all();
    }

    /**
     * 获取试卷的最新审核记录
     *
     * @param int $paperId 试卷ID
     * @param int $schoolId 学校ID
     * @return static|null
     */
    public static function getLatestAuditLog(int $paperId, int $schoolId): ?static
    {
        return static::find()
            ->where(['paper_id' => $paperId, 'school_id' => $schoolId])
            ->orderBy('created_at DESC')
            ->one();
    }

    /**
     * 获取指定状态的审核记录数量
     *
     * @param int $schoolId 学校ID
     * @param int $status 审核状态
     * @param string|null $startDate 开始日期
     * @param string|null $endDate 结束日期
     * @return int
     */
    public static function getAuditCountByStatus(int $schoolId, int $status, ?string $startDate = null, ?string $endDate = null): int
    {
        $query = static::find()
            ->where(['school_id' => $schoolId, 'status' => $status]);

        if ($startDate) {
            $query->andWhere(['>=', 'created_at', strtotime($startDate)]);
        }

        if ($endDate) {
            $query->andWhere(['<=', 'created_at', strtotime($endDate . ' 23:59:59')]);
        }

        return $query->count();
    }

    /**
     * 获取审核统计数据
     *
     * @param int $schoolId 学校ID
     * @param string|null $startDate 开始日期
     * @param string|null $endDate 结束日期
     * @return array
     */
    public static function getAuditStatistics(int $schoolId, ?string $startDate = null, ?string $endDate = null): array
    {
        return [
            'submitted' => static::getAuditCountByStatus($schoolId, self::STATUS_SUBMITTED, $startDate, $endDate),
            'approved' => static::getAuditCountByStatus($schoolId, self::STATUS_APPROVED, $startDate, $endDate),
            'rejected' => static::getAuditCountByStatus($schoolId, self::STATUS_REJECTED, $startDate, $endDate),
        ];
    }
}
