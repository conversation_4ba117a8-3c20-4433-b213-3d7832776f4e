<?php

namespace common\models\rbac;

use common\enums\Grade;
use common\models\base\Course;
use common\models\base\User;
use common\models\school\School;
use common\validators\EnumValidator;
use yii\behaviors\BlameableBehavior;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * 学科组长权限范围模型
 *
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $school_id 学校ID
 * @property string $course_code 科目代码
 * @property int|Grade|null $grade 年级，null表示所有年级
 * @property int|null $created_at
 * @property int|null $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 *
 * @property User $user 用户
 * @property School $school 学校
 * @property Course $course 科目
 */
class SubjectLeaderScope extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return '{{%subject_leader_scope}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors(): array
    {
        return [
            TimestampBehavior::class,
            BlameableBehavior::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['user_id', 'school_id', 'course_code'], 'required'],
            [['user_id', 'school_id', 'created_at', 'updated_at', 'created_by', 'updated_by'], 'integer'],
            [['course_code'], 'string', 'max' => 30],
            [['grade'], EnumValidator::class, 'enumClass' => Grade::class],
            
            [['user_id'], 'exist', 'targetClass' => User::class, 'targetAttribute' => 'id'],
            [['school_id'], 'exist', 'targetClass' => School::class, 'targetAttribute' => 'id'],
            [['course_code'], 'exist', 'targetClass' => Course::class, 'targetAttribute' => 'code'],
            
            // 唯一性验证
            [['user_id'], 'unique', 'targetAttribute' => ['user_id', 'school_id', 'course_code', 'grade']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'user_id' => '用户ID',
            'school_id' => '学校ID',
            'course_code' => '科目代码',
            'grade' => '年级',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
            'created_by' => '创建人',
            'updated_by' => '更新人',
        ];
    }

    /**
     * 获取用户关联
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * 获取学校关联
     */
    public function getSchool()
    {
        return $this->hasOne(School::class, ['id' => 'school_id']);
    }

    /**
     * 获取科目关联
     */
    public function getCourse()
    {
        return $this->hasOne(Course::class, ['code' => 'course_code']);
    }

    /**
     * 检查用户是否有指定学科和年级的权限
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @param string $courseCode 科目代码
     * @param int|Grade|null $grade 年级
     * @return bool
     */
    public static function hasPermission(int $userId, int $schoolId, string $courseCode, $grade = null): bool
    {
        $query = static::find()
            ->where([
                'user_id' => $userId,
                'school_id' => $schoolId,
                'course_code' => $courseCode,
            ]);

        // 如果指定了年级，检查是否有该年级的权限或者有全年级权限（grade为null）
        if ($grade !== null) {
            $query->andWhere(['or', ['grade' => $grade], ['grade' => null]]);
        }

        return $query->exists();
    }

    /**
     * 获取用户的权限范围
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @return array 返回格式：['course_code' => [grade1, grade2, ...], ...]
     */
    public static function getUserPermissionScope(int $userId, int $schoolId): array
    {
        /**
         * @var static[] $scopes
         */
        $scopes = static::find()
            ->where(['user_id' => $userId, 'school_id' => $schoolId])
            ->all();

        $result = [];
        foreach ($scopes as $scope) {
            if (!isset($result[$scope->course_code])) {
                $result[$scope->course_code] = [];
            }
            
            if ($scope->grade === null) {
                // 如果有全年级权限，返回特殊标记
                $result[$scope->course_code] = ['*'];
            } elseif (!in_array('*', $result[$scope->course_code])) {
                // 如果没有全年级权限，添加具体年级
                $result[$scope->course_code][] = $scope->grade;
            }
        }

        return $result;
    }

    /**
     * 批量设置用户的权限范围
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @param array $scopes 权限范围数组，格式：[['course_code' => 'math', 'grade' => 7], ...]
     * @return bool
     */
    public static function setUserPermissionScope(int $userId, int $schoolId, array $scopes): bool
    {
        $transaction = static::getDb()->beginTransaction();
        try {
            // 删除现有权限
            static::deleteAll(['user_id' => $userId, 'school_id' => $schoolId]);

            // 添加新权限
            foreach ($scopes as $scope) {
                $model = new static();
                $model->user_id = $userId;
                $model->school_id = $schoolId;
                $model->course_code = $scope['course_code'];
                $model->grade = $scope['grade'] ?? null;
                
                if (!$model->save()) {
                    throw new \Exception('保存权限范围失败');
                }
            }

            $transaction->commit();
            return true;
        } catch (\Exception $e) {
            $transaction->rollBack();
            return false;
        }
    }

    /**
     * 在保存后处理枚举类型
     */
    public function afterFind(): void
    {
        parent::afterFind();
        
        if ($this->grade !== null) {
            $this->grade = Grade::tryFrom($this->grade);
        }
    }

    /**
     * 在保存前处理枚举类型
     */
    public function beforeSave($insert): bool
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }

        if ($this->grade instanceof Grade) {
            $this->grade = $this->grade->value;
        }

        return true;
    }
}
