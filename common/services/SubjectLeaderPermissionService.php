<?php

namespace common\services;

use common\models\base\User;
use common\models\rbac\SubjectLeaderScope;
use common\models\school\School;
use yii\rbac\DbManager;
use Yii;

/**
 * 学科组长权限管理服务
 */
class SubjectLeaderPermissionService
{
    /**
     * 为用户分配学科组长权限
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @param array $scopes 权限范围 [['course_code' => 'math', 'grade' => 7], ...]
     * @return bool
     */
    public static function assignSubjectLeader(int $userId, int $schoolId, array $scopes): bool
    {
        $user = User::findOne($userId);
        $school = School::findOne($schoolId);
        
        if (!$user || !$school) {
            return false;
        }

        $auth = Yii::$app->authManager;
        
        try {
            // 分配学科组长角色
            $subjectLeaderRole = $auth->getRole('subject-leader');
            if (!$subjectLeaderRole) {
                return false;
            }
            
            if (!$auth->getAssignment('subject-leader', $userId)) {
                $auth->assign($subjectLeaderRole, $userId);
            }

            // 设置权限范围
            return SubjectLeaderScope::setUserPermissionScope($userId, $schoolId, $scopes);
        } catch (\Exception $e) {
            Yii::error("分配学科组长权限失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 移除用户的学科组长权限
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @return bool
     */
    public static function revokeSubjectLeader(int $userId, int $schoolId): bool
    {
        try {
            $auth = Yii::$app->authManager;
            
            // 移除学科组长角色
            $subjectLeaderRole = $auth->getRole('subject-leader');
            if ($subjectLeaderRole) {
                $auth->revoke($subjectLeaderRole, $userId);
            }
            
            // 清除权限范围
            SubjectLeaderScope::deleteAll(['user_id' => $userId, 'school_id' => $schoolId]);
            
            return true;
        } catch (\Exception $e) {
            Yii::error("移除学科组长权限失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 更新用户的权限范围
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @param array $scopes 新的权限范围
     * @return bool
     */
    public static function updatePermissionScope(int $userId, int $schoolId, array $scopes): bool
    {
        return SubjectLeaderScope::setUserPermissionScope($userId, $schoolId, $scopes);
    }

    /**
     * 获取用户的权限范围
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @return array
     */
    public static function getUserPermissionScope(int $userId, int $schoolId): array
    {
        return SubjectLeaderScope::getUserPermissionScope($userId, $schoolId);
    }

    /**
     * 检查用户是否是学科组长
     *
     * @param int $userId 用户ID
     * @return bool
     */
    public static function isSubjectLeader(int $userId): bool
    {
        $auth = Yii::$app->authManager;
        return $auth->checkAccess($userId, 'subject-leader');
    }

    /**
     * 获取学校的所有学科组长
     *
     * @param int $schoolId 学校ID
     * @return array 返回格式：[['user' => User, 'scopes' => [...]], ...]
     */
    public static function getSchoolSubjectLeaders(int $schoolId): array
    {
        $scopes = SubjectLeaderScope::find()
            ->with('user')
            ->where(['school_id' => $schoolId])
            ->all();

        $leaders = [];
        $userScopes = [];

        // 按用户分组
        foreach ($scopes as $scope) {
            $userId = $scope->user_id;
            if (!isset($userScopes[$userId])) {
                $userScopes[$userId] = [
                    'user' => $scope->user,
                    'scopes' => []
                ];
            }
            $userScopes[$userId]['scopes'][] = $scope;
        }

        return array_values($userScopes);
    }

    /**
     * 批量分配学科组长权限
     *
     * @param array $assignments 分配数据 [['user_id' => 1, 'school_id' => 1, 'scopes' => [...]], ...]
     * @return array 返回结果 ['success' => [...], 'failed' => [...]]
     */
    public static function batchAssignSubjectLeader(array $assignments): array
    {
        $success = [];
        $failed = [];

        foreach ($assignments as $assignment) {
            $userId = $assignment['user_id'] ?? null;
            $schoolId = $assignment['school_id'] ?? null;
            $scopes = $assignment['scopes'] ?? [];

            if ($userId && $schoolId && !empty($scopes)) {
                if (static::assignSubjectLeader($userId, $schoolId, $scopes)) {
                    $success[] = $assignment;
                } else {
                    $failed[] = $assignment;
                }
            } else {
                $failed[] = $assignment;
            }
        }

        return ['success' => $success, 'failed' => $failed];
    }

    /**
     * 验证权限范围数据格式
     *
     * @param array $scopes 权限范围数据
     * @return bool
     */
    public static function validateScopesFormat(array $scopes): bool
    {
        foreach ($scopes as $scope) {
            if (!is_array($scope) || !isset($scope['course_code'])) {
                return false;
            }
            
            // grade 可以为 null 或者是有效的年级值
            if (isset($scope['grade']) && $scope['grade'] !== null) {
                if (!is_numeric($scope['grade']) || $scope['grade'] < 1 || $scope['grade'] > 12) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 获取用户在指定学校的权限统计
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @return array 统计信息
     */
    public static function getUserPermissionStats(int $userId, int $schoolId): array
    {
        $scopes = SubjectLeaderScope::find()
            ->where(['user_id' => $userId, 'school_id' => $schoolId])
            ->all();

        $stats = [
            'total_courses' => 0,
            'total_grades' => 0,
            'courses' => [],
            'has_full_grade_permission' => false
        ];

        $courses = [];
        $grades = [];

        foreach ($scopes as $scope) {
            $courses[] = $scope->course_code;
            
            if ($scope->grade === null) {
                $stats['has_full_grade_permission'] = true;
            } else {
                $grades[] = $scope->grade;
            }
        }

        $stats['total_courses'] = count(array_unique($courses));
        $stats['total_grades'] = count(array_unique($grades));
        $stats['courses'] = array_unique($courses);

        return $stats;
    }
}
