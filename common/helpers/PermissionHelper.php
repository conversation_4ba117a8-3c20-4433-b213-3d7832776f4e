<?php

namespace common\helpers;

use common\models\base\User;
use common\rbac\SubjectLeaderRule;
use Yii;

/**
 * 权限验证辅助类
 */
class PermissionHelper
{
    /**
     * 检查当前用户是否有指定权限
     *
     * @param string $permission 权限名称
     * @param array $params 权限参数
     * @return bool
     */
    public static function can(string $permission, array $params = []): bool
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }

        return Yii::$app->user->can($permission, $params);
    }

    /**
     * 检查当前用户是否有学科组长权限
     *
     * @param string|null $courseCode 科目代码
     * @param int|null $grade 年级
     * @param int|null $schoolId 学校ID
     * @return bool
     */
    public static function canSubjectLeader(?string $courseCode = null, ?int $grade = null, ?int $schoolId = null): bool
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }

        $params = [];
        if ($courseCode !== null) {
            $params['course_code'] = $courseCode;
        }
        if ($grade !== null) {
            $params['grade'] = $grade;
        }
        if ($schoolId !== null) {
            $params['school_id'] = $schoolId;
        }

        return Yii::$app->user->can('subject-leader', $params);
    }

    /**
     * 检查当前用户是否可以查看校本题库
     * 学校内所有用户都有此权限（默认教师权限）
     *
     * @return bool
     */
    public static function canViewSchoolPaper(): bool
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }

        $user = Yii::$app->user->identity;
        // 只要是学校用户就有查看权限（所有学校用户都是教师）
        return $user instanceof User && $user->school !== null;
    }

    /**
     * 检查当前用户是否可以提交到校本题库
     * 学校内所有用户都有此权限（默认教师权限）
     *
     * @return bool
     */
    public static function canSubmitSchoolPaper(): bool
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }

        $user = Yii::$app->user->identity;
        // 只要是学校用户就有提交权限（所有学校用户都是教师）
        return $user instanceof User && $user->school !== null;
    }

    /**
     * 检查当前用户是否可以管理校本题库
     *
     * @param string|null $courseCode 科目代码
     * @param int|null $grade 年级
     * @return bool
     */
    public static function canManageSchoolPaper(?string $courseCode = null, ?int $grade = null): bool
    {
        $params = [];
        if ($courseCode !== null) {
            $params['course_code'] = $courseCode;
        }
        if ($grade !== null) {
            $params['grade'] = $grade;
        }

        return static::can('school-paper-manage', $params);
    }

    /**
     * 检查当前用户是否可以审核校本题库
     *
     * @param string|null $courseCode 科目代码
     * @param int|null $grade 年级
     * @return bool
     */
    public static function canAuditSchoolPaper(?string $courseCode = null, ?int $grade = null): bool
    {
        $params = [];
        if ($courseCode !== null) {
            $params['course_code'] = $courseCode;
        }
        if ($grade !== null) {
            $params['grade'] = $grade;
        }

        return static::can('school-paper-audit', $params);
    }

    /**
     * 检查当前用户是否可以管理试卷标签
     *
     * @param string|null $courseCode 科目代码
     * @param int|null $grade 年级
     * @return bool
     */
    public static function canManagePaperTag(?string $courseCode = null, ?int $grade = null): bool
    {
        $params = [];
        if ($courseCode !== null) {
            $params['course_code'] = $courseCode;
        }
        if ($grade !== null) {
            $params['grade'] = $grade;
        }

        return static::can('paper-tag-manage', $params);
    }

    /**
     * 获取当前用户有权限的科目列表
     *
     * @return array 科目代码数组
     */
    public static function getUserCourses(): array
    {
        if (Yii::$app->user->isGuest) {
            return [];
        }

        $user = Yii::$app->user->identity;
        if (!$user instanceof User || !$user->school) {
            return [];
        }

        // 如果是学校管理员，返回所有科目（系统管理员不参与学校业务）
        if (Yii::$app->user->can('school-admin')) {
            return \common\models\base\Course::find()->select('code')->column();
        }

        // 如果是学科组长，返回有权限的科目
        if (Yii::$app->user->can('subject-leader')) {
            return SubjectLeaderRule::getUserCourses($user->id, $user->school->id);
        }

        // 普通教师返回绑定的科目
        $schoolUser = $user->userSchools;
        if ($schoolUser && $schoolUser->course_codes) {
            return is_array($schoolUser->course_codes) ? $schoolUser->course_codes : [$schoolUser->course_codes];
        }

        return [];
    }

    /**
     * 获取当前用户在指定科目下有权限的年级列表
     *
     * @param string $courseCode 科目代码
     * @return array 年级数组
     */
    public static function getUserGrades(string $courseCode): array
    {
        if (Yii::$app->user->isGuest) {
            return [];
        }

        $user = Yii::$app->user->identity;
        if (!$user instanceof User || !$user->school) {
            return [];
        }

        // 如果是学校管理员，返回所有年级（系统管理员不参与学校业务）
        if (Yii::$app->user->can('school-admin')) {
            return array_map(fn($grade) => $grade->value, \common\enums\Grade::cases());
        }

        // 如果是学科组长，返回有权限的年级
        if (Yii::$app->user->can('subject-leader')) {
            $grades = SubjectLeaderRule::getUserGrades($user->id, $user->school->id, $courseCode);
            // 如果包含null，表示有全年级权限
            if (in_array(null, $grades)) {
                return array_map(fn($grade) => $grade->value, \common\enums\Grade::cases());
            }
            return array_filter($grades, fn($grade) => $grade !== null);
        }

        // 普通教师返回所在班级的年级
        $classes = $user->classes;
        $grades = [];
        foreach ($classes as $class) {
            if ($class->course_code === $courseCode) {
                $grades[] = $class->grade;
            }
        }

        return array_unique($grades);
    }

    /**
     * 为查询添加权限过滤条件
     *
     * @param \yii\db\ActiveQuery $query 查询对象
     * @param string $courseCodeField 科目代码字段名
     * @param string $gradeField 年级字段名
     * @return \yii\db\ActiveQuery
     */
    public static function applyPermissionFilter($query, string $courseCodeField = 'course_code', string $gradeField = 'grade')
    {
        if (Yii::$app->user->isGuest) {
            $query->andWhere('1=0');
            return $query;
        }

        $user = Yii::$app->user->identity;
        if (!$user instanceof User || !$user->school) {
            $query->andWhere('1=0');
            return $query;
        }

        // 如果是学校管理员，不添加过滤条件（系统管理员不参与学校业务）
        if (Yii::$app->user->can('school-admin')) {
            return $query;
        }

        // 如果是学科组长，应用学科组长权限过滤
        if (Yii::$app->user->can('subject-leader')) {
            return SubjectLeaderRule::applyPermissionFilter($query, $user->id, $user->school->id, $courseCodeField, $gradeField);
        }

        // 普通教师只能看到自己相关的内容
        $courses = static::getUserCourses();
        if (empty($courses)) {
            $query->andWhere('1=0');
            return $query;
        }

        $query->andWhere([$courseCodeField => $courses]);

        // 添加年级过滤
        $gradeConditions = ['or'];
        foreach ($courses as $courseCode) {
            $grades = static::getUserGrades($courseCode);
            if (!empty($grades)) {
                $gradeConditions[] = [
                    'and',
                    [$courseCodeField => $courseCode],
                    [$gradeField => $grades]
                ];
            }
        }

        if (count($gradeConditions) > 1) {
            $query->andWhere($gradeConditions);
        }

        return $query;
    }

    /**
     * 检查用户是否有权限访问指定的资源
     *
     * @param string $courseCode 科目代码
     * @param int|null $grade 年级
     * @param int|null $userId 用户ID，默认为当前用户
     * @return bool
     */
    public static function hasResourceAccess(string $courseCode, ?int $grade = null, ?int $userId = null): bool
    {
        if ($userId === null) {
            if (Yii::$app->user->isGuest) {
                return false;
            }
            $userId = Yii::$app->user->id;
        }

        $user = User::findOne($userId);
        if (!$user || !$user->school) {
            return false;
        }

        // 学校管理员有学校内所有权限（系统管理员不参与学校业务）
        if (Yii::$app->authManager->checkAccess($userId, 'school-admin')) {
            return true;
        }

        // 学科组长检查权限范围
        if (Yii::$app->authManager->checkAccess($userId, 'subject-leader')) {
            return \common\models\rbac\SubjectLeaderScope::hasPermission($userId, $user->school->id, $courseCode, $grade);
        }

        // 普通教师检查是否有该科目权限
        $schoolUser = $user->userSchools;
        if ($schoolUser && $schoolUser->course_codes) {
            $userCourses = is_array($schoolUser->course_codes) ? $schoolUser->course_codes : [$schoolUser->course_codes];
            return in_array($courseCode, $userCourses);
        }

        return false;
    }
}
