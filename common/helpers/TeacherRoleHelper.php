<?php

namespace common\helpers;

use common\models\base\User;
use Yii;

/**
 * 教师角色辅助类
 * 用于自动分配和管理教师默认角色
 */
class TeacherRoleHelper
{
    /**
     * 为学校用户自动分配教师角色
     *
     * @param int $userId 用户ID
     * @return bool
     */
    public static function assignTeacherRole(int $userId): bool
    {
        $user = User::findOne($userId);
        if (!$user || !$user->school) {
            return false;
        }

        $auth = Yii::$app->authManager;
        $teacherRole = $auth->getRole('teacher');
        
        if (!$teacherRole) {
            return false;
        }

        // 检查是否已经有教师角色
        if ($auth->getAssignment('teacher', $userId)) {
            return true; // 已经有角色了
        }

        try {
            $auth->assign($teacherRole, $userId);
            return true;
        } catch (\Exception $e) {
            Yii::error("分配教师角色失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查用户是否有教师角色
     *
     * @param int $userId 用户ID
     * @return bool
     */
    public static function hasTeacherRole(int $userId): bool
    {
        $auth = Yii::$app->authManager;
        return $auth->checkAccess($userId, 'teacher');
    }

    /**
     * 为所有学校用户批量分配教师角色
     *
     * @param int|null $schoolId 学校ID，如果为null则处理所有学校
     * @return array 返回处理结果
     */
    public static function batchAssignTeacherRole(?int $schoolId = null): array
    {
        $query = User::find()->joinWith('school');
        
        if ($schoolId !== null) {
            $query->andWhere(['school.id' => $schoolId]);
        }

        $users = $query->all();
        $success = 0;
        $failed = 0;

        foreach ($users as $user) {
            if (static::assignTeacherRole($user->id)) {
                $success++;
            } else {
                $failed++;
            }
        }

        return [
            'total' => count($users),
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * 移除用户的教师角色（通常在用户离开学校时调用）
     *
     * @param int $userId 用户ID
     * @return bool
     */
    public static function revokeTeacherRole(int $userId): bool
    {
        try {
            $auth = Yii::$app->authManager;
            $teacherRole = $auth->getRole('teacher');
            
            if ($teacherRole) {
                $auth->revoke($teacherRole, $userId);
            }
            
            return true;
        } catch (\Exception $e) {
            Yii::error("移除教师角色失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查并自动分配教师角色（可以在用户登录时调用）
     *
     * @param int $userId 用户ID
     * @return bool
     */
    public static function ensureTeacherRole(int $userId): bool
    {
        if (static::hasTeacherRole($userId)) {
            return true;
        }

        return static::assignTeacherRole($userId);
    }

    /**
     * 获取学校的所有教师用户
     *
     * @param int $schoolId 学校ID
     * @return User[]
     */
    public static function getSchoolTeachers(int $schoolId): array
    {
        $auth = Yii::$app->authManager;
        $teacherAssignments = $auth->getAssignments('teacher');
        
        if (empty($teacherAssignments)) {
            return [];
        }

        $userIds = array_keys($teacherAssignments);
        
        return User::find()
            ->joinWith('school')
            ->where(['user.id' => $userIds, 'school.id' => $schoolId])
            ->all();
    }

    /**
     * 统计学校的教师数量
     *
     * @param int $schoolId 学校ID
     * @return int
     */
    public static function countSchoolTeachers(int $schoolId): int
    {
        return count(static::getSchoolTeachers($schoolId));
    }
}
