<?php

namespace common\rbac;

use common\models\base\User;
use common\models\rbac\SubjectLeaderScope;
use yii\rbac\Rule;
use Yii;

/**
 * 学科组长权限验证规则
 * 
 * 用于验证学科组长是否有权限访问指定学科和年级的资源
 */
class SubjectLeaderRule extends Rule
{
    public $name = 'subjectLeaderRule';

    /**
     * 执行权限验证
     *
     * @param string|int $user 用户ID
     * @param \yii\rbac\Item $item 权限项
     * @param array $params 验证参数
     *   - course_code: 科目代码
     *   - grade: 年级
     *   - school_id: 学校ID（可选，如果不提供则从当前用户获取）
     * @return bool 是否有权限
     */
    public function execute($user, $item, $params)
    {
        // 获取用户对象
        $userModel = null;
        if (is_numeric($user)) {
            $userModel = User::findOne($user);
        } elseif ($user instanceof User) {
            $userModel = $user;
        }

        if (!$userModel) {
            return false;
        }

        // 如果用户不是学科组长，检查是否是学校管理员
        $authManager = Yii::$app->authManager;
        if (!$authManager->checkAccess($userModel->id, 'subject-leader')) {
            // 只有学校管理员不受学科年级限制，系统管理员不参与学校业务
            return $authManager->checkAccess($userModel->id, 'school-admin');
        }

        // 获取验证参数
        $courseCode = $params['course_code'] ?? null;
        $grade = $params['grade'] ?? null;
        $schoolId = $params['school_id'] ?? null;

        // 如果没有提供学校ID，从用户信息中获取
        if ($schoolId === null) {
            $school = $userModel->school;
            if (!$school) {
                return false;
            }
            $schoolId = $school->id;
        }

        // 如果没有提供科目代码，表示不需要验证科目权限
        if ($courseCode === null) {
            return true;
        }

        // 验证学科组长是否有该科目和年级的权限
        return SubjectLeaderScope::hasPermission($userModel->id, $schoolId, $courseCode, $grade);
    }

    /**
     * 检查用户是否有指定科目的权限（不考虑年级）
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @param string $courseCode 科目代码
     * @return bool
     */
    public static function hasCoursePermission(int $userId, int $schoolId, string $courseCode): bool
    {
        return SubjectLeaderScope::find()
            ->where([
                'user_id' => $userId,
                'school_id' => $schoolId,
                'course_code' => $courseCode,
            ])
            ->exists();
    }

    /**
     * 检查用户是否有指定年级的权限（不考虑科目）
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @param int $grade 年级
     * @return bool
     */
    public static function hasGradePermission(int $userId, int $schoolId, int $grade): bool
    {
        return SubjectLeaderScope::find()
            ->where([
                'user_id' => $userId,
                'school_id' => $schoolId,
            ])
            ->andWhere(['or', ['grade' => $grade], ['grade' => null]])
            ->exists();
    }

    /**
     * 获取用户有权限的科目列表
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @return array 科目代码数组
     */
    public static function getUserCourses(int $userId, int $schoolId): array
    {
        return SubjectLeaderScope::find()
            ->select('course_code')
            ->where(['user_id' => $userId, 'school_id' => $schoolId])
            ->distinct()
            ->column();
    }

    /**
     * 获取用户在指定科目下有权限的年级列表
     *
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @param string $courseCode 科目代码
     * @return array 年级数组，如果包含null则表示有全年级权限
     */
    public static function getUserGrades(int $userId, int $schoolId, string $courseCode): array
    {
        return SubjectLeaderScope::find()
            ->select('grade')
            ->where([
                'user_id' => $userId,
                'school_id' => $schoolId,
                'course_code' => $courseCode,
            ])
            ->column();
    }

    /**
     * 为查询添加学科组长权限过滤条件
     *
     * @param \yii\db\ActiveQuery $query 查询对象
     * @param int $userId 用户ID
     * @param int $schoolId 学校ID
     * @param string $courseCodeField 科目代码字段名
     * @param string $gradeField 年级字段名
     * @return \yii\db\ActiveQuery
     */
    public static function applyPermissionFilter($query, int $userId, int $schoolId, string $courseCodeField = 'course_code', string $gradeField = 'grade')
    {
        // 获取用户的权限范围
        $scopes = SubjectLeaderScope::find()
            ->where(['user_id' => $userId, 'school_id' => $schoolId])
            ->all();

        if (empty($scopes)) {
            // 如果没有任何权限，返回空结果
            $query->andWhere('1=0');
            return $query;
        }

        $conditions = ['or'];
        foreach ($scopes as $scope) {
            $condition = [$courseCodeField => $scope->course_code];

            // 如果指定了年级，添加年级条件
            if ($scope->grade !== null) {
                $condition[$gradeField] = $scope->grade;
            }

            $conditions[] = $condition;
        }

        $query->andWhere($conditions);
        return $query;
    }
}
